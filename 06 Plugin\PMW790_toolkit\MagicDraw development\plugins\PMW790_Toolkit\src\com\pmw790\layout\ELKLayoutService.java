package com.pmw790.layout;

import com.nomagic.magicdraw.openapi.uml.SessionManager;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;

import javax.swing.*;
import java.awt.Point;
import java.util.Map;

import static com.pmw790.functions.Utilities.Log;

/**
 * Main service for Eclipse Layout Kernel (ELK) integration with MagicDraw
 * Provides automatic graph layout for FID (Functional Interface Diagram) creation
 * with proper transaction management and threading
 */
public class ELKLayoutService {

    /**
     * Check if ELK libraries are available in the classpath
     * @return true if ELK can be used, false otherwise
     */
    public static boolean isELKAvailable() {
        return true;
    }

    /**
     * Apply ELK layout to FID with cabinet grouping and structured data optimization
     * Uses structured part/port data from Python to optimize element processing
     * 
     * @param diagram The FID diagram presentation element to layout  
     * @param systemCabinetMap Cabinet grouping data from rearrange_items()
     * @param spec Structured data containing parts/ports information from Python
     */
    public static void layoutFID(DiagramPresentationElement diagram,
                                 Object systemCabinetMap, Object spec) {
        if (!isELKAvailable()) {
            Log("ELK not available for FID layout - using standard layout");
            applyFallbackLayout(diagram);
            return;
        }

        try {
            // Convert MagicDraw FID to ELK graph using structured data from Python
            FIDDiagramConverter converter = new FIDDiagramConverter();
            Object elkGraph = converter.convertFIDToElkGraph(diagram, systemCabinetMap, spec);
            Log("-".repeat(50));

//            FIDLayouter layouter = new FIDLayouter();
//            Object layoutResult = layouter.layoutFID(elkGraph);
            
//            Log("ELK FID Layout: Converting layout results back to MagicDraw FID coordinates...");
//
            // Convert ELK results back to MagicDraw coordinate system
//            Map<PresentationElement, Point> coordinates = converter.extractLayoutCoordinates(layoutResult, diagram);

            // Apply results in MagicDraw transaction
//            applyLayoutResults(diagram, coordinates);
            
        } catch (Exception e) {
            Log("ELK FID layout failed with structured data: " + e.getMessage());
            Log("Falling back to standard grouping layout...");
            // Fallback to standard layout
            applyFallbackLayout(diagram);
        }
    }

    /**
     * Apply layout coordinates to presentation elements within a MagicDraw transaction
     * 
     * @param diagram The target diagram
     * @param coordinates Map of presentation elements to their new coordinates
     */
    private static void applyLayoutResults(DiagramPresentationElement diagram, 
                                          Map<PresentationElement, Point> coordinates) {
        SessionManager sessionManager = SessionManager.getInstance();
        sessionManager.createSession("ELK Diagram Layout");
        
        try {
            // Apply new coordinates to each presentation element
            for (Map.Entry<PresentationElement, Point> entry : coordinates.entrySet()) {
                PresentationElement element = entry.getKey();
                Point newPosition = entry.getValue();
                
                if (element.isEditable()) {
                    try {
                        // Create new bounds with updated position
                        java.awt.Rectangle currentBounds = element.getBounds();
                        java.awt.Rectangle newBounds = new java.awt.Rectangle(
                            newPosition.x, newPosition.y,
                            currentBounds.width, currentBounds.height
                        );
                        
                        // Apply the new bounds
                        element.setBounds(newBounds);
                        
                    } catch (Exception e) {
                        Log("Warning: Could not update position for element: " + e.getMessage());
                    }
                }
            }
            
            sessionManager.closeSession();
            Log("ELK layout applied successfully");
            
        } catch (Exception e) {
            sessionManager.cancelSession();
            Log("Error applying ELK layout results: " + e.getMessage());
            applyFallbackLayout(diagram);
        }
    }

    /**
     * Fallback to standard MagicDraw layout when ELK is not available or fails
     * 
     * @param diagram The diagram to layout
     */
    private static void applyFallbackLayout(DiagramPresentationElement diagram) {
        try {
            diagram.layout(true);
            
            // Schedule a second layout pass on the UI thread after a short delay
            SwingUtilities.invokeLater(() -> {
                try {
                    diagram.layout(true, new com.nomagic.magicdraw.uml.symbols.layout.ClassDiagramLayouter());
                } catch (Exception e) {
                    // Silently handle layout exceptions
                }
            });
        } catch (Exception e) {
            Log("Warning: Could not apply fallback layout: " + e.getMessage());
        }
    }
}