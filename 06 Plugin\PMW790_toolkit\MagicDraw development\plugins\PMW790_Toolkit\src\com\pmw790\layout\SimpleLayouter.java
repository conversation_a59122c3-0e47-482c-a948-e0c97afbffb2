package com.pmw790.layout;

import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.magicdraw.uml.symbols.paths.PathElement;
import com.nomagic.magicdraw.uml.symbols.shapes.PartView;
import com.nomagic.magicdraw.uml.symbols.shapes.PortView;

import java.awt.Point;
import java.awt.Rectangle;
import java.util.*;

import static com.pmw790.functions.Utilities.Log;

/**
 * Lightweight, standalone layout that does not depend on ELK.
 *
 * Goals:
 * - Group parts into "cabinets" if systemCabinetMap is provided
 * - Arrange parts in layers left-to-right to reduce crossings (Sugiyama-style lite)
 * - Compute coordinates for parts (nodes); let MagicDraw re-route connectors after placement
 *
 * Notes:
 * - We derive the graph from connector PathElements to infer adjacency between parts
 * - Ports are not repositioned (kept as-is); MD will reconnect/route paths based on new part positions
 */
public class SimpleLayouter {

    public Map<PresentationElement, Point> computeLayout(
            DiagramPresentationElement diagram,
            Object systemCabinetMap,
            Object spec
    ) {
        try {
            // 1) Collect parts from spec (preferred), fallback to diagram scan
            List<PresentationElement> parts = collectPartsFromSpec(diagram, spec);
            if (parts.isEmpty()) {
                parts = collectPartsFromDiagram(diagram);
            }

            // 2) Build undirected adjacency between parts from connector data
            Map<PresentationElement, Set<PresentationElement>> adj = buildAdjacency(diagram, spec);

            Log(String.format("SimpleLayouter: parts=%d, edges=%d",
                    parts.size(), countEdges(adj)));

            // 3) Group parts by cabinet if possible
            Map<String, List<PresentationElement>> groups = groupByCabinet(systemCabinetMap, parts);
            if (groups.isEmpty()) {
                groups.put("ALL", new ArrayList<>(parts));
            }

            // 4) Compute layout per group (layered left-to-right) and place groups in rows
            Map<PresentationElement, Point> coords = new HashMap<>();
            int groupX = 50;
            int groupY = 50;
            int maxRowHeight = 0;
            int groupsPerRow = Math.max(1, (int) Math.floor(Math.sqrt(groups.size())));
            int groupIndex = 0;

            for (Map.Entry<String, List<PresentationElement>> entry : groups.entrySet()) {
                List<PresentationElement> groupParts = entry.getValue();
                if (groupParts.isEmpty()) continue;

                GroupLayout gl = layoutGroup(groupParts, adj);
                // Translate group positions by (groupX, groupY)
                for (Map.Entry<PresentationElement, Point> e : gl.positions.entrySet()) {
                    Point p = e.getValue();
                    coords.put(e.getKey(), new Point(groupX + p.x, groupY + p.y));
                }

                // Advance group placement
                groupX += gl.width + 200; // spacing between groups
                maxRowHeight = Math.max(maxRowHeight, gl.height);
                groupIndex++;
                if (groupIndex % groupsPerRow == 0) {
                    groupX = 50;
                    groupY += maxRowHeight + 200; // spacing between rows
                    maxRowHeight = 0;
                }
            }

            Log("SimpleLayouter: computed coordinates for " + coords.size() + " elements");
            return coords;
        } catch (Exception ex) {
            Log("SimpleLayouter: layout failed: " + ex.getMessage());
            return Collections.emptyMap();
        }
    }

    // --- Group layout (layered, simple crossing reduction) ---

    private static class GroupLayout {
        Map<PresentationElement, Point> positions = new HashMap<>();
        int width;
        int height;
    }

    private GroupLayout layoutGroup(List<PresentationElement> parts,
                                    Map<PresentationElement, Set<PresentationElement>> adj) {
        // Parameters
        int hSpacing = 160; // horizontal spacing between layers
        int vSpacing = 80;  // vertical spacing between nodes within a layer
        int margin = 20;

        // 1) Build layers using BFS from a high-degree seed
        PresentationElement seed = parts.get(0);
        seed = parts.stream().max(Comparator.comparingInt(p -> degree(adj, p))).orElse(seed);
        Map<PresentationElement, Integer> layer = bfsLayers(seed, parts, adj);
        // Unreached nodes: start BFS from them too
        for (PresentationElement p : parts) {
            if (!layer.containsKey(p)) {
                layer.putAll(bfsLayers(p, parts, adj));
            }
        }

        // 2) Bucket nodes by layer
        int maxLayer = layer.values().stream().mapToInt(i -> i).max().orElse(0);
        List<List<PresentationElement>> layers = new ArrayList<>();
        for (int i = 0; i <= maxLayer; i++) layers.add(new ArrayList<>());
        for (PresentationElement p : parts) layers.get(layer.get(p)).add(p);

        // 3) Order within layers using barycenter heuristic repeatedly
        for (int pass = 0; pass < 2; pass++) {
            for (int i = 1; i <= maxLayer; i++) {
                final List<PresentationElement> prev = layers.get(i - 1);
                final Map<PresentationElement, Integer> indexPrev = indexMap(prev);
                layers.get(i).sort(Comparator.comparingDouble(n -> barycenter(n, adj, indexPrev)));
            }
            for (int i = maxLayer - 1; i >= 0; i--) {
                final List<PresentationElement> next = layers.get(i + 1);
                final Map<PresentationElement, Integer> indexNext = indexMap(next);
                layers.get(i).sort(Comparator.comparingDouble(n -> barycenter(n, adj, indexNext)));
            }
        }

        // 4) Assign coordinates
        GroupLayout gl = new GroupLayout();
        int x = margin;
        int totalHeight = 0;
        int groupWidth = 0;

        for (int i = 0; i <= maxLayer; i++) {
            List<PresentationElement> layerNodes = layers.get(i);
            int y = margin;
            int layerWidth = 0;
            // Compute max width in this layer for alignment
            for (PresentationElement p : layerNodes) {
                Rectangle b = p.getBounds();
                layerWidth = Math.max(layerWidth, b.width);
            }
            // Place nodes top-to-bottom
            for (PresentationElement p : layerNodes) {
                Rectangle b = p.getBounds();
                gl.positions.put(p, new Point(x, y));
                y += b.height + vSpacing;
            }
            totalHeight = Math.max(totalHeight, y);
            groupWidth += layerWidth + hSpacing;
            x += layerWidth + hSpacing;
        }

        gl.width = Math.max(groupWidth, 1);
        gl.height = Math.max(totalHeight, 1);
        return gl;
    }

    private static Map<PresentationElement, Integer> indexMap(List<PresentationElement> list) {
        Map<PresentationElement, Integer> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++) map.put(list.get(i), i);
        return map;
    }

    private static double barycenter(PresentationElement n,
                                     Map<PresentationElement, Set<PresentationElement>> adj,
                                     Map<PresentationElement, Integer> indexRef) {
        Set<PresentationElement> neigh = adj.getOrDefault(n, Collections.emptySet());
        int sum = 0, count = 0;
        for (PresentationElement v : neigh) {
            Integer idx = indexRef.get(v);
            if (idx != null) { sum += idx; count++; }
        }
        return count == 0 ? Integer.MAX_VALUE : (double) sum / count;
    }

    private static int degree(Map<PresentationElement, Set<PresentationElement>> adj, PresentationElement p) {
        return adj.getOrDefault(p, Collections.emptySet()).size();
    }

    private Map<PresentationElement, Integer> bfsLayers(PresentationElement seed,
                                                        List<PresentationElement> parts,
                                                        Map<PresentationElement, Set<PresentationElement>> adj) {
        Map<PresentationElement, Integer> layer = new HashMap<>();
        Queue<PresentationElement> q = new ArrayDeque<>();
        layer.put(seed, 0);
        q.add(seed);
        Set<PresentationElement> partSet = new HashSet<>(parts);
        while (!q.isEmpty()) {
            PresentationElement u = q.poll();
            int lu = layer.get(u);
            for (PresentationElement v : adj.getOrDefault(u, Collections.emptySet())) {
                if (partSet.contains(v) && !layer.containsKey(v)) {
                    layer.put(v, lu + 1);
                    q.add(v);
                }
            }
        }
        return layer;
    }

    // --- Data collection ---

    private List<PresentationElement> collectPartsFromSpec(DiagramPresentationElement diagram, Object spec) {
        List<PresentationElement> parts = new ArrayList<>();
        if (spec instanceof Map) {
            @SuppressWarnings("unchecked") Map<String, Object> map = (Map<String, Object>) spec;
            Object partsData = map.get("parts_data");
            if (partsData instanceof List) {
                for (Object o : (List<?>) partsData) {
                    if (o instanceof Map) {
                        Object pe = ((Map<?, ?>) o).get("presentation_element");
                        if (pe instanceof PresentationElement && ((PresentationElement) pe).isVisible()) {
                            parts.add((PresentationElement) pe);
                        }
                    }
                }
            }
        }
        return parts;
    }

    private List<PresentationElement> collectPartsFromDiagram(DiagramPresentationElement diagram) {
        List<PresentationElement> parts = new ArrayList<>();
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe instanceof PartView && pe.isVisible()) parts.add(pe);
        }
        return parts;
    }

    private Map<PresentationElement, Set<PresentationElement>> buildAdjacency(DiagramPresentationElement diagram, Object spec) {
        Map<PresentationElement, Set<PresentationElement>> adj = new HashMap<>();
        // helper lambda
        java.util.function.BiConsumer<PresentationElement, PresentationElement> addEdge = (a, b) -> {
            if (a == null || b == null || a == b) return;
            adj.computeIfAbsent(a, k -> new HashSet<>()).add(b);
            adj.computeIfAbsent(b, k -> new HashSet<>()).add(a);
        };

        // From spec if available
        if (spec instanceof Map) {
            @SuppressWarnings("unchecked") Map<String, Object> map = (Map<String, Object>) spec;
            Object connData = map.get("connector_data");
            if (connData instanceof List) {
                for (Object o : (List<?>) connData) {
                    if (o instanceof Map) {
                        Object pe = ((Map<?, ?>) o).get("presentation_element");
                        if (pe instanceof PathElement) {
                            PresentationElement a = toPart(((PathElement) pe).getClient());
                            PresentationElement b = toPart(((PathElement) pe).getSupplier());
                            addEdge.accept(a, b);
                        }
                    }
                }
            }
        }
        // Fallback: scan diagram for connectors
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe instanceof PathElement && pe.isVisible()) {
                PresentationElement a = toPart(((PathElement) pe).getClient());
                PresentationElement b = toPart(((PathElement) pe).getSupplier());
                addEdge.accept(a, b);
            }
        }
        return adj;
    }

    private PresentationElement toPart(PresentationElement endpoint) {
        if (endpoint == null) return null;
        if (endpoint instanceof PartView) return endpoint;
        if (endpoint instanceof PortView) {
            PresentationElement parent = endpoint.getParent();
            while (parent != null && !(parent instanceof PartView)) parent = parent.getParent();
            return parent instanceof PartView ? parent : null;
        }
        // Other types: climb up
        PresentationElement parent = endpoint.getParent();
        while (parent != null && !(parent instanceof PartView)) parent = parent.getParent();
        return parent instanceof PartView ? parent : null;
    }

    private int countEdges(Map<PresentationElement, Set<PresentationElement>> adj) {
        int m = 0;
        for (Set<PresentationElement> s : adj.values()) m += s.size();
        return m / 2;
    }
}

